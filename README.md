# Common components aka Fiori

A collection of commonly used components with no external dependencies.

## License

This project is licensed under [MIT](LICENSE) license.

## Code of conduct

This project follows and enforces the Contributor Covenant's [code of conduct](CODE_OF_CONDUCT.md).

### Examples of behavior that contributes to a positive environment for our community include:

- Demonstrating empathy and kindness toward other people
- Being respectful of differing opinions, viewpoints, and experiences
- Giving and gracefully accepting constructive feedback

### Examples of unacceptable behavior include:

- The use of sexualized language or imagery, and sexual attention or advances of any kind
- Trolling, insulting or derogatory comments, and personal or political attacks
- Public or private harassment

Please take a moment to read and familiarise yourself with the [complete code of conduct](CODE_OF_CONDUCT.md) as this will govern your interactions and contributions to this project.
