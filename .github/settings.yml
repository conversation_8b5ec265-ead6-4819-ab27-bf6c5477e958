repository:
  # For this to work you need to have the following app installed and configured for all repositories https://github.com/repository-settings/app,
  # see https://github.com/repository-settings/app/blob/master/docs/configuration.md for available settings.

  # A short description of the repository that will show up on GitHub
  description: A collection of commonly used components as either HTML or Web Components
  
  # A URL with more information about the repository
  homepage: https://github.com/schalkneethling/common-components

  # A comma-separated list of topics to set on the repository
  topics: HTML, CSS, JavaScript, Web Components

  # The branch used by default for pull requests and when the repository is cloned/viewed.
  default_branch: main

  # Either true to enable the wiki for this repository or false to disable it.
  has_wiki: false

  # Disable squash merges on GitHub
  allow_squash_merge: false

  # Disable rebase merges on GitHub
  allow_rebase_merge: false

  # Enable auto merge
  allow_auto_merge: true

  # Delete head branch on merge
  delete_branch_on_merge: true

  # Either `true` to enable automated security fixes, or `false` to disable
  # automated security fixes.
  enable_automated_security_fixes: true

  # Either `true` to enable vulnerability alerts, or `false` to disable
  # vulnerability alerts.
  enable_vulnerability_alerts: true

# Branch protection rules
# NOTE: Branch protection rules do not currently work: https://github.com/repository-settings/app/issues/857
branches:
  - name: main
    protection:
      # Required. Require at least one approving review on a pull request, before merging. Set to null to disable.
      required_pull_request_reviews:
        # The number of approvals required. (1-6)
        required_approving_review_count: 1
        # Dismiss approved reviews automatically when a new commit is pushed.
        dismiss_stale_reviews: true
        # Blocks merge until code owners have reviewed.
        require_code_owner_reviews: true
      # Required. Require status checks to pass before merging. Set to null to disable
      required_status_checks: null
      # Required. Enforce all configured restrictions for administrators. Set to true to enforce required status checks for repository administrators. Set to null to disable.
      enforce_admins: false
      # Prevent merge commits from being pushed to matching branches
      required_linear_history: false
      # Required. Restrict who can push to this branch. Team and user restrictions are only available for organization-owned repositories. Set to null to disable.
      restrictions: null

labels:
  - name: p0
    color: DE0F5A
    description: Stop everything.
  - name: p1
    color: C23434
    description: Top priority item.
  - name: p2
    color: C2BD34
    description: To be addressed after top priority items.
  - name: p3
    color: 73C234
    description: General maintenance. Prioritize during triage.
  - name: p4
    color: 34C2A9
    description: Nice to have. Help welcomed.
  - name: bug
    color: DE0F5A
    description: Something is not working as expected.
  - name: enhancement
    color: 0062F5
    description: New feature or improvement
  - name: help wanted
    color: 8435D5
    description: Open to everyone.
  - name: good first issue
    color: A954FF
    description: These are for first-time contributors only.
