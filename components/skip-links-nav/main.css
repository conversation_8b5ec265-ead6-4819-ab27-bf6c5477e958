/*
Keyboard & screen reader skip link menu
********************************************************************** */

.skip-links-nav * {
  box-sizing: border-box;
}

.skip-links-nav {
  --skip-links-nav-link-background-color: rgba(255 255 255 / 90%);
  --skip-links-nav-inline-size: 100%;
  --skip-links-nav-link-color: #212121;
  --skip-links-nav-outline: 0.0625rem solid #622aff;
  --skip-links-nav-padding: 1rem;
  --skip-links-nav-text-align: center;
  --skip-links-nav-z-index: 999;

  inline-size: var(--skip-links-nav-inline-size);
  inset-block-start: -20rem;
  inset-inline-start: 0;
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
}

.skip-links-nav a {
  background-color: var(--skip-links-nav-link-background-color);
  color: var(--skip-links-nav-link-color);
  display: block;
  inline-size: var(--skip-links-nav-inline-size);
  inset-block-start: 0;
  opacity: 0;
  outline: var(--skip-links-nav-outline);
  padding-block: var(--skip-links-nav-padding);
  position: absolute;
  text-align: var(--skip-links-nav-text-align);
  transition:
    inset-block-start 0.3s ease-in-out,
    opacity 0.5s ease-in-out;
}

.skip-links-nav a:focus {
  inset-block-start: 20rem;
  opacity: 1;
  text-decoration: none;
  z-index: var(--skip-links-nav-z-index);
}

/* https://www.tpgi.com/the-anatomy-of-visually-hidden/ */
.visually-hidden:not(:focus, :active) {
  block-size: 1px;
  clip-path: inset(50%);
  inline-size: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
}

@media screen and (prefers-reduced-motion: reduce) {
  .skip-links-nav a {
    transition: none;
  }
}
